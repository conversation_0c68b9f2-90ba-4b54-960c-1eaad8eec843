<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ActivityLog;
use App\Models\SystemConfig;
use App\Models\User;
use Illuminate\Http\Request;

class SystemConfigController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware(['auth', 'role:' . User::ROLE_SUPERADMIN]);
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $configs = SystemConfig::orderBy('group')->orderBy('key')->paginate(20);

        return view('admin.system-configs.index', compact('configs'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.system-configs.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'key' => 'required|string|max:255|unique:system_configs',
            'value' => 'nullable|string',
            'group' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_encrypted' => 'boolean',
        ]);

        $config = SystemConfig::create($validated);

        // Log activity
        ActivityLog::log(
            'config_created',
            'Created system configuration: ' . $config->key,
            $config
        );

        return redirect()->route('admin.system-configs.index')
            ->with('success', 'Configuration created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(SystemConfig $systemConfig)
    {
        return view('admin.system-configs.edit', compact('systemConfig'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, SystemConfig $systemConfig)
    {
        $validated = $request->validate([
            'value' => 'nullable|string',
            'group' => 'required|string|max:255',
            'description' => 'nullable|string',
            'is_encrypted' => 'boolean',
        ]);

        $systemConfig->update($validated);

        // Log activity
        ActivityLog::log(
            'config_updated',
            'Updated system configuration: ' . $systemConfig->key,
            $systemConfig
        );

        return redirect()->route('admin.system-configs.index')
            ->with('success', 'Configuration updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(SystemConfig $systemConfig)
    {
        $key = $systemConfig->key;

        $systemConfig->delete();

        // Log activity
        ActivityLog::log(
            'config_deleted',
            'Deleted system configuration: ' . $key,
            null,
            ['config_key' => $key]
        );

        return redirect()->route('admin.system-configs.index')
            ->with('success', 'Configuration deleted successfully.');
    }
}
