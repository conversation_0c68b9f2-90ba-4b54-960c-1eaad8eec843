<?php

namespace App\Http\Controllers;

use App\Facades\GoogleMeet;
use App\Models\ActivityLog;
use App\Models\Meeting;
use App\Models\MeetingSetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MeetingController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $meetings = Auth::user()->meetings()->latest()->paginate(10);

        return view('meetings.index', compact('meetings'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('meetings.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'start_time' => 'required|date',
            'end_time' => 'nullable|date|after:start_time',
            'is_recording_enabled' => 'boolean',
        ]);

        // Create the meeting
        $meeting = new Meeting();
        $meeting->fill($validated);
        $meeting->user_id = Auth::id();
        $meeting->status = Meeting::STATUS_SCHEDULED;
        $meeting->save();

        // Create meeting settings
        $settings = new MeetingSetting();
        $settings->meeting_id = $meeting->id;
        $settings->host_approval_required = $request->boolean('host_approval_required', false);
        $settings->screen_sharing_enabled = $request->boolean('screen_sharing_enabled', true);
        $settings->mute_on_entry = $request->boolean('mute_on_entry', false);
        $settings->chat_enabled = $request->boolean('chat_enabled', true);
        $settings->join_before_host = $request->boolean('join_before_host', false);
        $settings->save();

        // Create Google Meet link
        try {
            $meetData = GoogleMeet::createMeeting($meeting);

            if ($meetData) {
                $meeting->meet_link = $meetData['meet_link'];
                $meeting->meet_id = $meetData['meet_id'];
                $meeting->save();

                // Log activity
                ActivityLog::log(
                    'meeting_created',
                    'Created a new meeting: ' . $meeting->title,
                    $meeting
                );

                return redirect()->route('meetings.show', $meeting)
                    ->with('success', 'Meeting created successfully.');
            }
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Failed to create Google Meet: ' . $e->getMessage());
        }

        // If Google Meet creation failed, still save the meeting but notify the user
        ActivityLog::log(
            'meeting_created_without_link',
            'Created a new meeting without Google Meet link: ' . $meeting->title,
            $meeting
        );

        return redirect()->route('meetings.show', $meeting)
            ->with('warning', 'Meeting created but Google Meet link could not be generated.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Meeting $meeting)
    {
        $this->authorize('view', $meeting);

        return view('meetings.show', compact('meeting'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Meeting $meeting)
    {
        $this->authorize('update', $meeting);

        return view('meetings.edit', compact('meeting'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Meeting $meeting)
    {
        $this->authorize('update', $meeting);

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'start_time' => 'required|date',
            'end_time' => 'nullable|date|after:start_time',
            'is_recording_enabled' => 'boolean',
        ]);

        $meeting->fill($validated);
        $meeting->save();

        // Update meeting settings
        $settings = $meeting->settings;
        $settings->host_approval_required = $request->boolean('host_approval_required', false);
        $settings->screen_sharing_enabled = $request->boolean('screen_sharing_enabled', true);
        $settings->mute_on_entry = $request->boolean('mute_on_entry', false);
        $settings->chat_enabled = $request->boolean('chat_enabled', true);
        $settings->join_before_host = $request->boolean('join_before_host', false);
        $settings->save();

        // Try to update Google Meet settings
        try {
            GoogleMeet::updateMeetingSettings($meeting, $settings);
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Failed to update Google Meet settings: ' . $e->getMessage());
        }

        // Log activity
        ActivityLog::log(
            'meeting_updated',
            'Updated meeting: ' . $meeting->title,
            $meeting
        );

        return redirect()->route('meetings.show', $meeting)
            ->with('success', 'Meeting updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Meeting $meeting)
    {
        $this->authorize('delete', $meeting);

        $title = $meeting->title;

        // Delete the meeting
        $meeting->delete();

        // Log activity
        ActivityLog::log(
            'meeting_deleted',
            'Deleted meeting: ' . $title,
            null,
            ['meeting_id' => $meeting->id]
        );

        return redirect()->route('meetings.index')
            ->with('success', 'Meeting deleted successfully.');
    }

    /**
     * Toggle recording for a meeting.
     */
    public function toggleRecording(Meeting $meeting)
    {
        $this->authorize('update', $meeting);

        $startRecording = !$meeting->is_recording_active;

        try {
            // Call Google Meet API to start/stop recording
            GoogleMeet::toggleRecording($meeting, $startRecording);

            // Update meeting status
            $meeting->is_recording_active = $startRecording;
            $meeting->save();

            // Log activity
            ActivityLog::log(
                $startRecording ? 'recording_started' : 'recording_stopped',
                ($startRecording ? 'Started' : 'Stopped') . ' recording for meeting: ' . $meeting->title,
                $meeting
            );

            return redirect()->route('meetings.show', $meeting)
                ->with('success', 'Recording ' . ($startRecording ? 'started' : 'stopped') . ' successfully.');
        } catch (\Exception $e) {
            // Log the error
            \Log::error('Failed to toggle recording: ' . $e->getMessage());

            return redirect()->route('meetings.show', $meeting)
                ->with('error', 'Failed to ' . ($startRecording ? 'start' : 'stop') . ' recording. Please try again.');
        }
    }
}
