<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Crypt;

class SystemConfig extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'key',
        'value',
        'group',
        'description',
        'is_encrypted',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_encrypted' => 'boolean',
    ];

    /**
     * Get the value attribute with decryption if needed.
     *
     * @param  string  $value
     * @return string
     */
    public function getValueAttribute($value)
    {
        if ($this->is_encrypted && !empty($value)) {
            return Crypt::decrypt($value);
        }

        return $value;
    }

    /**
     * Set the value attribute with encryption if needed.
     *
     * @param  string  $value
     * @return void
     */
    public function setValueAttribute($value)
    {
        if ($this->is_encrypted && !empty($value)) {
            $this->attributes['value'] = Crypt::encrypt($value);
        } else {
            $this->attributes['value'] = $value;
        }
    }

    /**
     * Get a config value by key.
     *
     * @param  string  $key
     * @param  mixed  $default
     * @return mixed
     */
    public static function getConfig(string $key, $default = null)
    {
        $config = self::where('key', $key)->first();

        return $config ? $config->value : $default;
    }

    /**
     * Set a config value by key.
     *
     * @param  string  $key
     * @param  mixed  $value
     * @param  string  $group
     * @param  string|null  $description
     * @param  bool  $isEncrypted
     * @return SystemConfig
     */
    public static function setConfig(string $key, $value, string $group = 'general', ?string $description = null, bool $isEncrypted = false)
    {
        $config = self::firstOrNew(['key' => $key]);

        $config->fill([
            'value' => $value,
            'group' => $group,
            'description' => $description,
            'is_encrypted' => $isEncrypted,
        ]);

        $config->save();

        return $config;
    }
}
