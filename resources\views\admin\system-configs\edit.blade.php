<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Edit System Configuration') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form method="POST" action="{{ route('admin.system-configs.update', $systemConfig) }}">
                        @csrf
                        @method('PUT')

                        <!-- Key (readonly) -->
                        <div class="mb-4">
                            <label for="key" class="block text-sm font-medium text-gray-700">Configuration Key</label>
                            <input type="text" name="key" id="key" value="{{ $systemConfig->key }}" 
                                   class="mt-1 block w-full rounded-md border-gray-300 bg-gray-100 shadow-sm" 
                                   readonly>
                            <p class="mt-1 text-sm text-gray-500">Configuration key cannot be changed</p>
                        </div>

                        <!-- Value -->
                        <div class="mb-4">
                            <label for="value" class="block text-sm font-medium text-gray-700">Value</label>
                            @if($systemConfig->is_encrypted && !empty($systemConfig->value))
                                <div class="mb-2">
                                    <p class="text-sm text-gray-600">Current value is encrypted. Leave blank to keep current value.</p>
                                </div>
                            @endif
                            <textarea name="value" id="value" rows="3"
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                      placeholder="{{ $systemConfig->is_encrypted && !empty($systemConfig->value) ? 'Leave blank to keep current encrypted value' : '' }}">{{ $systemConfig->is_encrypted ? '' : old('value', $systemConfig->value) }}</textarea>
                            @error('value')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Group -->
                        <div class="mb-4">
                            <label for="group" class="block text-sm font-medium text-gray-700">Group</label>
                            <select name="group" id="group" 
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" 
                                    required>
                                <option value="">Select a group</option>
                                <option value="google_api" {{ old('group', $systemConfig->group) === 'google_api' ? 'selected' : '' }}>Google API</option>
                                <option value="general" {{ old('group', $systemConfig->group) === 'general' ? 'selected' : '' }}>General</option>
                                <option value="meetings" {{ old('group', $systemConfig->group) === 'meetings' ? 'selected' : '' }}>Meetings</option>
                                <option value="email" {{ old('group', $systemConfig->group) === 'email' ? 'selected' : '' }}>Email</option>
                            </select>
                            @error('group')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="mb-4">
                            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                            <textarea name="description" id="description" rows="2"
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('description', $systemConfig->description) }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Is Encrypted -->
                        <div class="mb-6">
                            <label class="flex items-center">
                                <input type="checkbox" name="is_encrypted" value="1" 
                                       class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                       {{ old('is_encrypted', $systemConfig->is_encrypted) ? 'checked' : '' }}>
                                <span class="ml-2 text-sm text-gray-600">Encrypt this value (for sensitive data like passwords, API keys)</span>
                            </label>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex items-center justify-end space-x-3">
                            <a href="{{ route('admin.system-configs.index') }}" 
                               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" 
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Update Configuration
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
