<?php

namespace App\Services;

use App\Models\Meeting;
use App\Models\MeetingSetting;
use App\Models\SystemConfig;
use Google\Client as GoogleClient;
use Google\Service\Calendar;
use Google\Service\Calendar\ConferenceData;
use Google\Service\Calendar\ConferenceSolutionKey;
use Google\Service\Calendar\CreateConferenceRequest;
use Google\Service\Calendar\Event;
use Google\Service\Calendar\EventDateTime;
use Google\Service\Exception as GoogleServiceException;
use Illuminate\Support\Facades\Log;
use Exception;

class GoogleMeetService
{
    protected GoogleClient $client;
    protected ?Calendar $calendarService = null;

    /**
     * Create a new GoogleMeetService instance.
     */
    public function __construct()
    {
        $this->client = new GoogleClient();
        $this->client->setApplicationName(config('app.name'));
        $this->client->setScopes([
            Calendar::CALENDAR,
            Calendar::CALENDAR_EVENTS,
        ]);
        $this->client->setAccessType('offline');
        $this->client->setPrompt('select_account consent');

        // Load credentials from system config
        $this->loadCredentials();
    }

    /**
     * Load Google API credentials from system config.
     */
    protected function loadCredentials(): void
    {
        $clientId = SystemConfig::getConfig('google_client_id');
        $clientSecret = SystemConfig::getConfig('google_client_secret');
        $refreshToken = SystemConfig::getConfig('google_refresh_token');

        if (!$clientId || !$clientSecret) {
            Log::warning('Google API credentials not configured');
            return;
        }

        $this->client->setClientId($clientId);
        $this->client->setClientSecret($clientSecret);

        if ($refreshToken) {
            $this->client->refreshToken($refreshToken);
        }

        // Initialize Calendar service
        $this->calendarService = new Calendar($this->client);
    }

    /**
     * Create a new Google Meet meeting.
     *
     * @param Meeting $meeting
     * @return array|null
     */
    public function createMeeting(Meeting $meeting): ?array
    {
        if (!$this->calendarService) {
            throw new \RuntimeException('Google Calendar service not initialized');
        }

        try {
            // Create a new Calendar event
            $event = new Event([
                'summary' => $meeting->title,
                'description' => $meeting->description,
                'start' => new EventDateTime([
                    'dateTime' => $meeting->start_time->format('c'),
                    'timeZone' => config('app.timezone'),
                ]),
                'end' => new EventDateTime([
                    'dateTime' => $meeting->end_time ? $meeting->end_time->format('c') : $meeting->start_time->addHour()->format('c'),
                    'timeZone' => config('app.timezone'),
                ]),
                // Add Google Meet conference data
                'conferenceData' => new ConferenceData([
                    'createRequest' => new CreateConferenceRequest([
                        'requestId' => uniqid(),
                        'conferenceSolutionKey' => new ConferenceSolutionKey([
                            'type' => 'hangoutsMeet',
                        ]),
                    ]),
                ]),
            ]);

            // Insert the event
            $createdEvent = $this->calendarService->events->insert(
                'primary',
                $event,
                ['conferenceDataVersion' => 1]
            );

            // Extract meeting details
            $meetLink = $createdEvent->getHangoutLink();
            $meetId = $this->extractMeetId($meetLink);

            return [
                'meet_link' => $meetLink,
                'meet_id' => $meetId,
                'event_id' => $createdEvent->getId(),
            ];
        } catch (GoogleServiceException $e) {
            Log::error('Google Meet API error: ' . $e->getMessage(), [
                'meeting_id' => $meeting->id,
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
            ]);

            throw new Exception('Failed to create Google Meet: ' . $e->getMessage());
        } catch (Exception $e) {
            Log::error('Unexpected error creating Google Meet: ' . $e->getMessage(), [
                'meeting_id' => $meeting->id,
                'error' => $e->getMessage(),
            ]);

            throw new Exception('Failed to create Google Meet: ' . $e->getMessage());
        }
    }

    /**
     * Extract the Meet ID from the Meet link.
     *
     * @param string|null $meetLink
     * @return string|null
     */
    protected function extractMeetId(?string $meetLink): ?string
    {
        if (!$meetLink) {
            return null;
        }

        // Extract the meeting ID from the URL
        // Format: https://meet.google.com/abc-defg-hij
        $parts = explode('/', $meetLink);
        return end($parts);
    }

    /**
     * Update meeting settings.
     *
     * @param Meeting $meeting
     * @param MeetingSetting $settings
     * @return bool
     */
    public function updateMeetingSettings(Meeting $meeting, MeetingSetting $settings): bool
    {
        // Note: Google Meet API has limited capabilities for modifying meeting settings
        // This is a placeholder for future implementation when Google expands their API

        return true;
    }

    /**
     * Start or stop recording for a meeting.
     *
     * @param Meeting $meeting
     * @param bool $startRecording
     * @return bool
     */
    public function toggleRecording(Meeting $meeting, bool $startRecording): bool
    {
        // Note: Recording control via API is limited and may require additional permissions
        // This is a placeholder for future implementation

        return true;
    }
}
