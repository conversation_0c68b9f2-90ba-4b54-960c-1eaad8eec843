<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('meeting_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('meeting_id')->constrained()->onDelete('cascade');
            $table->boolean('host_approval_required')->default(false);
            $table->boolean('screen_sharing_enabled')->default(true);
            $table->boolean('mute_on_entry')->default(false);
            $table->boolean('chat_enabled')->default(true);
            $table->boolean('join_before_host')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('meeting_settings');
    }
};
