<?php

namespace App\Console\Commands;

use App\Models\SystemConfig;
use App\Models\User;
use Illuminate\Console\Command;

class TestSetup extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'meet:test-setup';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the Google Meet Manager application setup';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Google Meet Manager Setup...');
        $this->newLine();

        // Test database connection
        $this->info('1. Testing database connection...');
        try {
            $userCount = User::count();
            $this->info("   ✓ Database connected. Found {$userCount} users.");
        } catch (\Exception $e) {
            $this->error("   ✗ Database connection failed: " . $e->getMessage());
            return 1;
        }

        // Test user roles
        $this->info('2. Testing user roles...');
        $superAdmins = User::where('role', User::ROLE_SUPERADMIN)->count();
        $regularUsers = User::where('role', User::ROLE_USER)->count();
        $this->info("   ✓ Found {$superAdmins} super admin(s) and {$regularUsers} regular user(s).");

        // Test system configurations
        $this->info('3. Testing system configurations...');
        $configs = SystemConfig::count();
        $this->info("   ✓ Found {$configs} system configuration(s).");

        // Test Google API configuration
        $this->info('4. Testing Google API configuration...');
        $googleClientId = SystemConfig::getConfig('google_client_id');
        $googleClientSecret = SystemConfig::getConfig('google_client_secret');

        if (empty($googleClientId) || empty($googleClientSecret)) {
            $this->warn('   ⚠ Google API credentials not configured. Please set them in the admin panel.');
        } else {
            $this->info('   ✓ Google API credentials are configured.');
        }

        // Test middleware
        $this->info('5. Testing middleware registration...');
        $middlewareAliases = app('router')->getMiddleware();
        if (isset($middlewareAliases['role'])) {
            $this->info('   ✓ Role middleware is registered.');
        } else {
            $this->error('   ✗ Role middleware is not registered.');
        }

        $this->newLine();
        $this->info('Setup test completed!');
        $this->newLine();

        // Display login credentials
        $this->info('Default login credentials:');
        $this->table(
            ['Email', 'Password', 'Role'],
            [
                ['<EMAIL>', 'password', 'Super Admin'],
                ['<EMAIL>', 'password', 'Regular User'],
            ]
        );

        return 0;
    }
}
