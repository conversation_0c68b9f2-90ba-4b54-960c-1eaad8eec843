<?php

namespace Database\Seeders;

use App\Models\SystemConfig;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create default superadmin user
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'password' => Hash::make('password'),
                'role' => User::ROLE_SUPERADMIN,
                'email_verified_at' => now(),
            ]
        );

        // Create a regular user for testing
        User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test User',
                'password' => Hash::make('password'),
                'role' => User::ROLE_USER,
                'email_verified_at' => now(),
            ]
        );

        // Create default system configurations for Google API
        $googleConfigs = [
            [
                'key' => 'google_client_id',
                'value' => '',
                'group' => 'google_api',
                'description' => 'Google OAuth2 Client ID for Google Meet API',
                'is_encrypted' => false,
            ],
            [
                'key' => 'google_client_secret',
                'value' => '',
                'group' => 'google_api',
                'description' => 'Google OAuth2 Client Secret for Google Meet API',
                'is_encrypted' => true,
            ],
            [
                'key' => 'google_refresh_token',
                'value' => '',
                'group' => 'google_api',
                'description' => 'Google OAuth2 Refresh Token for Google Meet API',
                'is_encrypted' => true,
            ],
            [
                'key' => 'app_timezone',
                'value' => 'UTC',
                'group' => 'general',
                'description' => 'Default timezone for the application',
                'is_encrypted' => false,
            ],
            [
                'key' => 'max_meeting_duration',
                'value' => '480',
                'group' => 'meetings',
                'description' => 'Maximum meeting duration in minutes (default: 8 hours)',
                'is_encrypted' => false,
            ],
        ];

        foreach ($googleConfigs as $config) {
            SystemConfig::firstOrCreate(
                ['key' => $config['key']],
                $config
            );
        }
    }
}
