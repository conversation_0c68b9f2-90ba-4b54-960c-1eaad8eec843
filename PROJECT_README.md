# Geexar Meets - Professional Meeting Management Platform ✅

A modern Laravel 11 application for managing Google Meet sessions with advanced features, role-based access control, and beautiful UI/UX design.

## 🚀 Quick Start

1. **Start the application**:
   ```bash
   php artisan serve
   ```

2. **Visit**: http://127.0.0.1:8000

3. **Login with Super Admin credentials**:
   - **Email**: `<EMAIL>`
   - **Password**: `MeetManager2024!@#`

## ✅ What's Been Set Up

- ✅ **Laravel 11** with PHP 8.3
- ✅ **MySQL Database** (`meet_manager`) with all tables migrated
- ✅ **User Authentication** with <PERSON><PERSON>ze
- ✅ **Role-Based Access Control** (SuperAdmin & Regular User)
- ✅ **Google Meet API Integration** ready for configuration
- ✅ **Admin Panel** with full CRUD operations
- ✅ **Meeting Management** system
- ✅ **Activity Logging** for audit trails
- ✅ **Responsive UI** with Tailwind CSS

## � Google API Setup (Required)

To enable Google Meet integration, you need to set up Google API credentials:

### Step 1: Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the following APIs:
   - **Google Calendar API**
   - **Google Meet API** (if available in your region)
   - **Google Workspace Admin SDK** (for advanced features)

### Step 2: Create OAuth 2.0 Credentials

1. Navigate to **APIs & Services** → **Credentials**
2. Click **+ CREATE CREDENTIALS** → **OAuth client ID**
3. Choose **Web application**
4. Configure:
   - **Name**: `Geexar Meets`
   - **Authorized JavaScript origins**: `http://localhost:8000`, `https://yourdomain.com`
   - **Authorized redirect URIs**:
     - `http://localhost:8000/auth/google/callback`
     - `https://yourdomain.com/auth/google/callback`
5. Download the JSON file with your credentials

### Step 3: Configure Application

1. **Login as Super Admin**: `<EMAIL>`
2. **Navigate to**: Admin → System Configuration
3. **Set the following values**:
   ```
   google_client_id: YOUR_CLIENT_ID_FROM_JSON
   google_client_secret: YOUR_CLIENT_SECRET_FROM_JSON
   google_redirect_uri: http://localhost:8000/auth/google/callback
   ```

### Step 4: Generate Refresh Token (Optional)

For server-to-server operations, you may need a refresh token:

1. Use Google OAuth 2.0 Playground: https://developers.google.com/oauthplayground/
2. Select **Calendar API v3** scopes
3. Authorize and get refresh token
4. Add to System Configuration: `google_refresh_token`

### Step 5: Test Integration

1. **Create a test meeting** from the dashboard
2. **Verify** that Google Calendar events are created
3. **Check** meeting links are generated properly

## 📋 Next Steps

1. **Complete Google API setup** (see above)
2. **Create your first meeting**
3. **Add team members** (Admin → Users)
4. **Customize settings** (Admin → System Config)

## 🛠️ Useful Commands

```bash
# Test application setup
php artisan meet:test-setup

# Check migration status
php artisan migrate:status

# Clear application cache
php artisan config:clear
```

## 👤 Default Users

| Email | Password | Role |
|-------|----------|------|
| <EMAIL> | MeetManager2024!@# | Super Admin |
| <EMAIL> | password | Regular User |

---
**Developed for Geexar** | Laravel 11 + PHP 8.3 + MySQL
