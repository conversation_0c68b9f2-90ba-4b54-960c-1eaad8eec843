<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Create New Meeting') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <x-breadcrumb :items="[
                ['title' => 'Meetings', 'url' => route('meetings.index')],
                ['title' => 'Create New Meeting']
            ]" />
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <form method="POST" action="{{ route('meetings.store') }}">
                        @csrf

                        <!-- Title -->
                        <div class="mb-4">
                            <label for="title" class="block text-sm font-medium text-gray-700">Meeting Title</label>
                            <input type="text" name="title" id="title" value="{{ old('title') }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                   required>
                            @error('title')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Description -->
                        <div class="mb-4">
                            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                            <textarea name="description" id="description" rows="3"
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('description') }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Start Time -->
                        <div class="mb-4">
                            <label for="start_time" class="block text-sm font-medium text-gray-700">Start Time</label>
                            <input type="datetime-local" name="start_time" id="start_time" value="{{ old('start_time') }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                   required>
                            @error('start_time')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- End Time -->
                        <div class="mb-4">
                            <label for="end_time" class="block text-sm font-medium text-gray-700">End Time (Optional)</label>
                            <input type="datetime-local" name="end_time" id="end_time" value="{{ old('end_time') }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('end_time')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Recording Settings -->
                        <div class="mb-6">
                            <div class="flex items-center">
                                <input type="checkbox" name="is_recording_enabled" id="is_recording_enabled" value="1"
                                       {{ old('is_recording_enabled') ? 'checked' : '' }}
                                       class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <label for="is_recording_enabled" class="ml-2 block text-sm text-gray-900">
                                    Enable Recording
                                </label>
                            </div>
                        </div>

                        <!-- Meeting Settings -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Meeting Settings</h3>

                            <div class="space-y-3">
                                <div class="flex items-center">
                                    <input type="checkbox" name="host_approval_required" id="host_approval_required" value="1"
                                           {{ old('host_approval_required') ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <label for="host_approval_required" class="ml-2 block text-sm text-gray-900">
                                        Require host approval to join
                                    </label>
                                </div>

                                <div class="flex items-center">
                                    <input type="checkbox" name="screen_sharing_enabled" id="screen_sharing_enabled" value="1"
                                           {{ old('screen_sharing_enabled', true) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <label for="screen_sharing_enabled" class="ml-2 block text-sm text-gray-900">
                                        Enable screen sharing
                                    </label>
                                </div>

                                <div class="flex items-center">
                                    <input type="checkbox" name="mute_on_entry" id="mute_on_entry" value="1"
                                           {{ old('mute_on_entry') ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <label for="mute_on_entry" class="ml-2 block text-sm text-gray-900">
                                        Mute participants on entry
                                    </label>
                                </div>

                                <div class="flex items-center">
                                    <input type="checkbox" name="chat_enabled" id="chat_enabled" value="1"
                                           {{ old('chat_enabled', true) ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <label for="chat_enabled" class="ml-2 block text-sm text-gray-900">
                                        Enable chat
                                    </label>
                                </div>

                                <div class="flex items-center">
                                    <input type="checkbox" name="join_before_host" id="join_before_host" value="1"
                                           {{ old('join_before_host') ? 'checked' : '' }}
                                           class="rounded border-gray-300 text-indigo-600 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <label for="join_before_host" class="ml-2 block text-sm text-gray-900">
                                        Allow participants to join before host
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex items-center justify-end space-x-3">
                            <a href="{{ route('meetings.index') }}"
                               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Create Meeting
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
