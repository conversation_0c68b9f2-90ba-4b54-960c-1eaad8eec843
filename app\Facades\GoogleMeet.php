<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static array|null createMeeting(\App\Models\Meeting $meeting)
 * @method static bool updateMeetingSettings(\App\Models\Meeting $meeting, \App\Models\MeetingSetting $settings)
 * @method static bool toggleRecording(\App\Models\Meeting $meeting, bool $startRecording)
 * 
 * @see \App\Services\GoogleMeetService
 */
class GoogleMeet extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'google.meet';
    }
}
