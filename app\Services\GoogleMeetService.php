<?php

namespace App\Services;

use App\Models\Meeting;
use App\Models\MeetingSetting;
use App\Models\SystemConfig;
use Google\Client as GoogleClient;
use Google\Service\Calendar;
use Google\Service\Calendar\ConferenceData;
use Google\Service\Calendar\ConferenceSolutionKey;
use Google\Service\Calendar\CreateConferenceRequest;
use Google\Service\Calendar\Event;
use Google\Service\Calendar\EventDateTime;
use Google\Service\Exception as GoogleServiceException;
use GuzzleHttp\Client as GuzzleClient;
use Illuminate\Support\Facades\Log;
use Exception;

class GoogleMeetService
{
    protected GoogleClient $client;
    protected ?Calendar $calendarService = null;

    /**
     * Create a new GoogleMeetService instance.
     */
    public function __construct()
    {
        $this->client = new GoogleClient();
        $this->client->setApplicationName(config('app.name'));
        $this->client->setScopes([
            Calendar::CALENDAR,
            Calendar::CALENDAR_EVENTS,
        ]);
        $this->client->setAccessType('offline');
        $this->client->setPrompt('select_account consent');

        // Configure HTTP client options for SSL
        $this->configureHttpClient();

        // Load credentials from system config
        $this->loadCredentials();
    }

    /**
     * Configure HTTP client options for SSL and other settings.
     */
    protected function configureHttpClient(): void
    {
        // Configure HTTP client options
        $httpClientOptions = [
            'timeout' => 30,   // Set timeout to 30 seconds
        ];

        // Handle SSL verification based on environment
        if (app()->environment('production')) {
            // In production, enable SSL verification
            $httpClientOptions['verify'] = true;
        } else {
            // For development, try to use proper CA bundle or disable verification
            $caBundlePath = $this->getCaBundlePath();

            if ($caBundlePath && file_exists($caBundlePath)) {
                // Use CA bundle if available
                $httpClientOptions['verify'] = $caBundlePath;
                Log::info('Using CA bundle for SSL verification: ' . $caBundlePath);
            } else {
                // Disable SSL verification for development (not recommended for production)
                $httpClientOptions['verify'] = false;
                Log::warning('SSL verification disabled for Google API calls. This is not recommended for production.');
            }
        }

        // Set HTTP client options
        $this->client->setHttpClient(new GuzzleClient($httpClientOptions));
    }

    /**
     * Get the path to the CA bundle file.
     *
     * @return string|null
     */
    protected function getCaBundlePath(): ?string
    {
        // Common CA bundle locations
        $possiblePaths = [
            // Windows with XAMPP
            'C:\xampp\apache\bin\curl-ca-bundle.crt',
            // Windows with WAMP
            'C:\wamp64\bin\php\php' . PHP_MAJOR_VERSION . '.' . PHP_MINOR_VERSION . '.' . PHP_RELEASE_VERSION . '\extras\ssl\cacert.pem',
            // Laravel storage path
            storage_path('app/cacert.pem'),
            // System default
            ini_get('curl.cainfo'),
            ini_get('openssl.cafile'),
        ];

        foreach ($possiblePaths as $path) {
            if ($path && file_exists($path)) {
                return $path;
            }
        }

        return null;
    }

    /**
     * Load Google API credentials from system config.
     */
    protected function loadCredentials(): void
    {
        $clientId = SystemConfig::getConfig('google_client_id');
        $clientSecret = SystemConfig::getConfig('google_client_secret');
        $refreshToken = SystemConfig::getConfig('google_refresh_token');

        if (!$clientId || !$clientSecret) {
            Log::warning('Google API credentials not configured');
            return;
        }

        $this->client->setClientId($clientId);
        $this->client->setClientSecret($clientSecret);

        if ($refreshToken) {
            $this->client->refreshToken($refreshToken);
        }

        // Initialize Calendar service
        $this->calendarService = new Calendar($this->client);
    }

    /**
     * Check if Google API is properly configured.
     *
     * @return bool
     */
    public function isConfigured(): bool
    {
        $clientId = SystemConfig::getConfig('google_client_id');
        $clientSecret = SystemConfig::getConfig('google_client_secret');

        return !empty($clientId) && !empty($clientSecret);
    }

    /**
     * Get configuration status and missing requirements.
     *
     * @return array
     */
    public function getConfigurationStatus(): array
    {
        $clientId = SystemConfig::getConfig('google_client_id');
        $clientSecret = SystemConfig::getConfig('google_client_secret');
        $refreshToken = SystemConfig::getConfig('google_refresh_token');

        $missing = [];
        if (empty($clientId)) $missing[] = 'google_client_id';
        if (empty($clientSecret)) $missing[] = 'google_client_secret';

        return [
            'configured' => empty($missing),
            'has_refresh_token' => !empty($refreshToken),
            'missing_configs' => $missing,
            'message' => empty($missing)
                ? 'Google API is properly configured'
                : 'Missing required Google API configuration: ' . implode(', ', $missing)
        ];
    }

    /**
     * Create a new Google Meet meeting.
     *
     * @param Meeting $meeting
     * @return array|null
     */
    public function createMeeting(Meeting $meeting): ?array
    {
        if (!$this->isConfigured()) {
            throw new \RuntimeException('Google API is not properly configured. Please configure Google Client ID and Client Secret in System Configuration.');
        }

        if (!$this->calendarService) {
            throw new \RuntimeException('Google Calendar service could not be initialized. Please check your API credentials.');
        }

        try {
            // Create a new Calendar event
            $event = new Event([
                'summary' => $meeting->title,
                'description' => $meeting->description,
                'start' => new EventDateTime([
                    'dateTime' => $meeting->start_time->format('c'),
                    'timeZone' => config('app.timezone'),
                ]),
                'end' => new EventDateTime([
                    'dateTime' => $meeting->end_time ? $meeting->end_time->format('c') : $meeting->start_time->addHour()->format('c'),
                    'timeZone' => config('app.timezone'),
                ]),
                // Add Google Meet conference data
                'conferenceData' => new ConferenceData([
                    'createRequest' => new CreateConferenceRequest([
                        'requestId' => uniqid(),
                        'conferenceSolutionKey' => new ConferenceSolutionKey([
                            'type' => 'hangoutsMeet',
                        ]),
                    ]),
                ]),
            ]);

            // Insert the event
            $createdEvent = $this->calendarService->events->insert(
                'primary',
                $event,
                ['conferenceDataVersion' => 1]
            );

            // Extract meeting details
            $meetLink = $createdEvent->getHangoutLink();
            $meetId = $this->extractMeetId($meetLink);

            return [
                'meet_link' => $meetLink,
                'meet_id' => $meetId,
                'event_id' => $createdEvent->getId(),
            ];
        } catch (GoogleServiceException $e) {
            Log::error('Google Meet API error: ' . $e->getMessage(), [
                'meeting_id' => $meeting->id,
                'error' => $e->getMessage(),
                'error_code' => $e->getCode(),
            ]);

            throw new Exception('Failed to create Google Meet: ' . $e->getMessage());
        } catch (Exception $e) {
            Log::error('Unexpected error creating Google Meet: ' . $e->getMessage(), [
                'meeting_id' => $meeting->id,
                'error' => $e->getMessage(),
            ]);

            throw new Exception('Failed to create Google Meet: ' . $e->getMessage());
        }
    }

    /**
     * Extract the Meet ID from the Meet link.
     *
     * @param string|null $meetLink
     * @return string|null
     */
    protected function extractMeetId(?string $meetLink): ?string
    {
        if (!$meetLink) {
            return null;
        }

        // Extract the meeting ID from the URL
        // Format: https://meet.google.com/abc-defg-hij
        $parts = explode('/', $meetLink);
        return end($parts);
    }

    /**
     * Update meeting settings.
     *
     * @param Meeting $meeting
     * @param MeetingSetting $settings
     * @return bool
     */
    public function updateMeetingSettings(Meeting $meeting, MeetingSetting $settings): bool
    {
        // Note: Google Meet API has limited capabilities for modifying meeting settings
        // This is a placeholder for future implementation when Google expands their API

        return true;
    }

    /**
     * Start or stop recording for a meeting.
     *
     * @param Meeting $meeting
     * @param bool $startRecording
     * @return bool
     */
    public function toggleRecording(Meeting $meeting, bool $startRecording): bool
    {
        // Note: Recording control via API is limited and may require additional permissions
        // This is a placeholder for future implementation

        return true;
    }

    /**
     * Test SSL configuration by making a simple API call.
     *
     * @return array
     */
    public function testSslConfiguration(): array
    {
        try {
            // Create a simple HTTP client with the same configuration
            $caBundlePath = $this->getCaBundlePath();
            $httpClientOptions = ['timeout' => 10];

            if (app()->environment('production')) {
                $httpClientOptions['verify'] = true;
            } else {
                if ($caBundlePath && file_exists($caBundlePath)) {
                    $httpClientOptions['verify'] = $caBundlePath;
                } else {
                    $httpClientOptions['verify'] = false;
                }
            }

            $client = new GuzzleClient($httpClientOptions);

            // Test with a simple Google API endpoint
            $response = $client->get('https://www.googleapis.com/calendar/v3/users/me/calendarList', [
                'headers' => [
                    'Authorization' => 'Bearer invalid_token_for_test'
                ]
            ]);

            return [
                'success' => true,
                'message' => 'SSL configuration is working',
                'ssl_verify' => $httpClientOptions['verify'],
                'ca_bundle_path' => $caBundlePath
            ];
        } catch (\GuzzleHttp\Exception\ClientException $e) {
            // 401 Unauthorized is expected with invalid token, but means SSL is working
            if ($e->getCode() === 401) {
                return [
                    'success' => true,
                    'message' => 'SSL configuration is working (401 expected with invalid token)',
                    'ssl_verify' => $httpClientOptions['verify'] ?? 'unknown',
                    'ca_bundle_path' => $caBundlePath ?? 'none'
                ];
            }

            return [
                'success' => false,
                'message' => 'SSL test failed: ' . $e->getMessage(),
                'ssl_verify' => $httpClientOptions['verify'] ?? 'unknown',
                'ca_bundle_path' => $caBundlePath ?? 'none'
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'SSL test failed: ' . $e->getMessage(),
                'ssl_verify' => $httpClientOptions['verify'] ?? 'unknown',
                'ca_bundle_path' => $caBundlePath ?? 'none'
            ];
        }
    }
}
