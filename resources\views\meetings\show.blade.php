<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ $meeting->title }}
            </h2>
            <div class="space-x-2">
                <a href="{{ route('meetings.edit', $meeting) }}"
                   class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                    Edit
                </a>
                <form action="{{ route('meetings.destroy', $meeting) }}" method="POST" class="inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit"
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                            onclick="return confirm('Are you sure you want to delete this meeting?')">
                        Delete
                    </button>
                </form>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <!-- Meeting Details -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Meeting Information</h3>

                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Title</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $meeting->title }}</p>
                                </div>

                                @if($meeting->description)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Description</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $meeting->description }}</p>
                                    </div>
                                @endif

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Start Time</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $meeting->start_time->format('F j, Y \a\t g:i A') }}</p>
                                </div>

                                @if($meeting->end_time)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">End Time</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $meeting->end_time->format('F j, Y \a\t g:i A') }}</p>
                                    </div>
                                @endif

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Status</label>
                                    <span class="mt-1 px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                        @if($meeting->status === 'scheduled') bg-yellow-100 text-yellow-800
                                        @elseif($meeting->status === 'active') bg-green-100 text-green-800
                                        @elseif($meeting->status === 'completed') bg-blue-100 text-blue-800
                                        @else bg-red-100 text-red-800
                                        @endif">
                                        {{ ucfirst($meeting->status) }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Meeting Access</h3>

                            @if($meeting->meet_link)
                                <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-green-800">Meeting Link Available</h3>
                                            <div class="mt-2">
                                                <a href="{{ $meeting->meet_link }}" target="_blank"
                                                   class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                                    Join Google Meet
                                                </a>
                                            </div>
                                            <div class="mt-2">
                                                <p class="text-xs text-green-700">Meeting ID: {{ $meeting->meet_id ?? 'N/A' }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @else
                                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-yellow-800">No Meeting Link</h3>
                                            <p class="mt-1 text-sm text-yellow-700">The Google Meet link could not be generated. This usually means Google API is not configured.</p>
                                            @if(Auth::user()->isSuperAdmin())
                                                <div class="mt-2">
                                                    <a href="{{ route('admin.system-configs.index') }}"
                                                       class="bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-1 px-3 rounded text-xs">
                                                        Configure Google API
                                                    </a>
                                                </div>
                                            @else
                                                <p class="mt-1 text-xs text-yellow-600">Please contact your administrator to configure Google API integration.</p>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <!-- Recording Controls -->
                            @if($meeting->is_recording_enabled)
                                <div class="mt-4">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">Recording Controls</h4>
                                    <form action="{{ route('meetings.toggle-recording', $meeting) }}" method="POST">
                                        @csrf
                                        <button type="submit"
                                                class="@if($meeting->is_recording_active) bg-red-500 hover:bg-red-700 @else bg-green-500 hover:bg-green-700 @endif text-white font-bold py-2 px-4 rounded">
                                            @if($meeting->is_recording_active)
                                                Stop Recording
                                            @else
                                                Start Recording
                                            @endif
                                        </button>
                                    </form>
                                    @if($meeting->is_recording_active)
                                        <p class="mt-1 text-xs text-red-600">Recording is currently active</p>
                                    @endif
                                </div>
                            @endif
                        </div>
                    </div>

                    <!-- Meeting Settings -->
                    @if($meeting->settings)
                        <div class="border-t border-gray-200 pt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Meeting Settings</h3>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="flex items-center">
                                    <span class="@if($meeting->settings->host_approval_required) text-green-600 @else text-gray-400 @endif">
                                        @if($meeting->settings->host_approval_required) ✓ @else ✗ @endif
                                    </span>
                                    <span class="ml-2 text-sm text-gray-900">Host approval required</span>
                                </div>

                                <div class="flex items-center">
                                    <span class="@if($meeting->settings->screen_sharing_enabled) text-green-600 @else text-gray-400 @endif">
                                        @if($meeting->settings->screen_sharing_enabled) ✓ @else ✗ @endif
                                    </span>
                                    <span class="ml-2 text-sm text-gray-900">Screen sharing enabled</span>
                                </div>

                                <div class="flex items-center">
                                    <span class="@if($meeting->settings->mute_on_entry) text-green-600 @else text-gray-400 @endif">
                                        @if($meeting->settings->mute_on_entry) ✓ @else ✗ @endif
                                    </span>
                                    <span class="ml-2 text-sm text-gray-900">Mute on entry</span>
                                </div>

                                <div class="flex items-center">
                                    <span class="@if($meeting->settings->chat_enabled) text-green-600 @else text-gray-400 @endif">
                                        @if($meeting->settings->chat_enabled) ✓ @else ✗ @endif
                                    </span>
                                    <span class="ml-2 text-sm text-gray-900">Chat enabled</span>
                                </div>

                                <div class="flex items-center">
                                    <span class="@if($meeting->settings->join_before_host) text-green-600 @else text-gray-400 @endif">
                                        @if($meeting->settings->join_before_host) ✓ @else ✗ @endif
                                    </span>
                                    <span class="ml-2 text-sm text-gray-900">Join before host</span>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
