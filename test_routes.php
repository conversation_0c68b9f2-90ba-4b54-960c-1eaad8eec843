<?php

/**
 * Quick route testing script
 * Tests all major routes to ensure they're accessible
 */

$baseUrl = 'http://127.0.0.1:8000';

$routes = [
    // Public routes
    '/' => 'Homepage',
    '/login' => 'Login Page',
    '/register' => 'Register Page',
    
    // Protected routes (should redirect to login)
    '/dashboard' => 'Dashboard',
    '/meetings' => 'Meetings Index',
    '/meetings/create' => 'Create Meeting',
    '/profile' => 'Profile',
    
    // Admin routes (should redirect to login)
    '/admin' => 'Admin Dashboard',
    '/admin/users' => 'Admin Users',
    '/admin/users/create' => 'Create User',
    '/admin/system-configs' => 'System Configs',
    '/admin/system-configs/create' => 'Create System Config',
    '/admin/activity-logs' => 'Activity Logs',
];

echo "Testing Google Meet Manager Routes...\n";
echo "=====================================\n\n";

$passed = 0;
$failed = 0;

foreach ($routes as $route => $description) {
    $url = $baseUrl . $route;
    
    // Use curl to test the route
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_NOBODY, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "❌ {$description} ({$route}): ERROR - {$error}\n";
        $failed++;
    } else {
        // 200 = OK, 302 = Redirect (expected for protected routes)
        if ($httpCode == 200 || $httpCode == 302) {
            $status = $httpCode == 200 ? 'OK' : 'REDIRECT';
            echo "✅ {$description} ({$route}): {$status} ({$httpCode})\n";
            $passed++;
        } else {
            echo "❌ {$description} ({$route}): HTTP {$httpCode}\n";
            $failed++;
        }
    }
}

echo "\n=====================================\n";
echo "Test Results:\n";
echo "✅ Passed: {$passed}\n";
echo "❌ Failed: {$failed}\n";
echo "Total: " . ($passed + $failed) . "\n";

if ($failed == 0) {
    echo "\n🎉 All routes are working correctly!\n";
} else {
    echo "\n⚠️  Some routes have issues. Please check the failed routes above.\n";
}

echo "\nNote: Protected routes showing 'REDIRECT' is expected behavior.\n";
echo "They redirect to login page when not authenticated.\n";
