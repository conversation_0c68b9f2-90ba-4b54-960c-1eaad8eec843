<?php

namespace App\Console\Commands;

use App\Facades\GoogleMeet;
use Illuminate\Console\Command;

class TestSslConfiguration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ssl:test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test SSL configuration for Google API calls';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing SSL configuration for Google API...');
        $this->info('');

        $result = GoogleMeet::testSslConfiguration();

        if ($result['success']) {
            $this->info('✅ ' . $result['message']);
        } else {
            $this->error('❌ ' . $result['message']);
        }

        $this->info('');
        $this->info('Configuration Details:');
        $this->info('- SSL Verify: ' . ($result['ssl_verify'] === false ? 'Disabled' : ($result['ssl_verify'] === true ? 'Enabled (system default)' : $result['ssl_verify'])));
        $this->info('- CA Bundle Path: ' . ($result['ca_bundle_path'] ?: 'None'));
        $this->info('- Environment: ' . app()->environment());

        return $result['success'] ? 0 : 1;
    }
}
