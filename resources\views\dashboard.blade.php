<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Dashboard') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="text-center">
                        <h3 class="text-2xl font-bold text-gray-900 mb-4">Welcome to Google Meet Manager</h3>
                        <p class="text-gray-600 mb-8">Manage your Google Meet meetings with ease</p>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto">
                            <a href="{{ route('meetings.index') }}"
                               class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-4 px-6 rounded-lg text-center block">
                                <svg class="h-8 w-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                                View My Meetings
                            </a>

                            <a href="{{ route('meetings.create') }}"
                               class="bg-green-500 hover:bg-green-700 text-white font-bold py-4 px-6 rounded-lg text-center block">
                                <svg class="h-8 w-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                Create New Meeting
                            </a>
                        </div>

                        @if(Auth::user()->isSuperAdmin())
                            <div class="mt-8 pt-8 border-t border-gray-200">
                                <h4 class="text-lg font-medium text-gray-900 mb-4">Admin Actions</h4>
                                <div class="flex justify-center space-x-4">
                                    <a href="{{ route('admin.dashboard') }}"
                                       class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                                        Admin Dashboard
                                    </a>
                                    <a href="{{ route('admin.users.index') }}"
                                       class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded">
                                        Manage Users
                                    </a>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
