<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;

class DownloadCaCertificate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ssl:download-ca-bundle {--force : Force download even if file exists}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Download CA certificate bundle for SSL verification';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $filePath = storage_path('app/cacert.pem');

        if (file_exists($filePath) && !$this->option('force')) {
            $this->info('CA certificate bundle already exists at: ' . $filePath);
            $this->info('Use --force to download again.');
            return 0;
        }

        $this->info('Downloading CA certificate bundle...');

        try {
            // Download the CA bundle from curl.se (official source)
            $response = Http::timeout(30)->get('https://curl.se/ca/cacert.pem');

            if ($response->successful()) {
                Storage::put('cacert.pem', $response->body());
                $this->info('✅ CA certificate bundle downloaded successfully to: ' . $filePath);
                $this->info('');
                $this->info('You can now use this for SSL verification in your Google API calls.');
                return 0;
            } else {
                $this->error('❌ Failed to download CA certificate bundle. HTTP status: ' . $response->status());
                return 1;
            }
        } catch (\Exception $e) {
            $this->error('❌ Error downloading CA certificate bundle: ' . $e->getMessage());
            $this->info('');
            $this->info('Alternative solutions:');
            $this->info('1. Download manually from: https://curl.se/ca/cacert.pem');
            $this->info('2. Save it to: ' . $filePath);
            $this->info('3. Or disable SSL verification for development (not recommended)');
            return 1;
        }
    }
}
