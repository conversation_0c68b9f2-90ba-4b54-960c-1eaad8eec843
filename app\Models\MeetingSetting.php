<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MeetingSetting extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'meeting_id',
        'host_approval_required',
        'screen_sharing_enabled',
        'mute_on_entry',
        'chat_enabled',
        'join_before_host',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'host_approval_required' => 'boolean',
        'screen_sharing_enabled' => 'boolean',
        'mute_on_entry' => 'boolean',
        'chat_enabled' => 'boolean',
        'join_before_host' => 'boolean',
    ];

    /**
     * Get the meeting that owns the settings.
     */
    public function meeting(): BelongsTo
    {
        return $this->belongsTo(Meeting::class);
    }
}
