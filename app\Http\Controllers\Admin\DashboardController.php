<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ActivityLog;
use App\Models\Meeting;
use App\Models\User;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get statistics for the dashboard
        $stats = [
            'total_users' => User::count(),
            'total_meetings' => Meeting::count(),
            'active_meetings' => Meeting::where('status', Meeting::STATUS_ACTIVE)->count(),
            'recent_activities' => ActivityLog::latest()->take(10)->get(),
        ];

        return view('admin.dashboard', compact('stats'));
    }
}
