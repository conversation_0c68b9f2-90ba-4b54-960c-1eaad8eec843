#!/bin/bash

echo "==================================="
echo "Google Meet Manager Setup Script"
echo "==================================="
echo ""

# Check if PHP is installed
if ! command -v php &> /dev/null; then
    echo "❌ PHP is not installed. Please install PHP 8.3 or higher."
    exit 1
fi

# Check PHP version
PHP_VERSION=$(php -r "echo PHP_VERSION;")
echo "✅ PHP Version: $PHP_VERSION"

# Check if Composer is installed
if ! command -v composer &> /dev/null; then
    echo "❌ Composer is not installed. Please install Composer."
    exit 1
fi

echo "✅ Composer is available"

# Install dependencies
echo ""
echo "📦 Installing PHP dependencies..."
composer install --no-dev --optimize-autoloader

# Generate application key
echo ""
echo "🔑 Generating application key..."
php artisan key:generate

# Run migrations
echo ""
echo "🗄️ Running database migrations..."
php artisan migrate --force

# Seed the database
echo ""
echo "🌱 Seeding database with initial data..."
php artisan db:seed --force

# Clear and cache config
echo ""
echo "⚡ Optimizing application..."
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Test the setup
echo ""
echo "🧪 Testing application setup..."
php artisan meet:test-setup

echo ""
echo "==================================="
echo "✅ Setup completed successfully!"
echo "==================================="
echo ""
echo "🚀 To start the application:"
echo "   php artisan serve"
echo ""
echo "🌐 Then visit: http://127.0.0.1:8000"
echo ""
echo "👤 Login credentials:"
echo "   Email: <EMAIL>"
echo "   Password: MeetManager2024!@#"
echo ""
echo "📝 Next steps:"
echo "   1. Login as Super Admin"
echo "   2. Go to Admin → System Config"
echo "   3. Configure Google API credentials"
echo "   4. Start creating meetings!"
echo ""
