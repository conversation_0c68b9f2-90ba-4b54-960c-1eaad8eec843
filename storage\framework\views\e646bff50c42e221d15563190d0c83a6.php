<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e($meeting->title); ?>

            </h2>
            <div class="space-x-2">
                <a href="<?php echo e(route('meetings.edit', $meeting)); ?>"
                   class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                    Edit
                </a>
                <form action="<?php echo e(route('meetings.destroy', $meeting)); ?>" method="POST" class="inline">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('DELETE'); ?>
                    <button type="submit"
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded"
                            onclick="return confirm('Are you sure you want to delete this meeting?')">
                        Delete
                    </button>
                </form>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <!-- Meeting Details -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Meeting Information</h3>

                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Title</label>
                                    <p class="mt-1 text-sm text-gray-900"><?php echo e($meeting->title); ?></p>
                                </div>

                                <?php if($meeting->description): ?>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Description</label>
                                        <p class="mt-1 text-sm text-gray-900"><?php echo e($meeting->description); ?></p>
                                    </div>
                                <?php endif; ?>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Start Time</label>
                                    <p class="mt-1 text-sm text-gray-900"><?php echo e($meeting->start_time->format('F j, Y \a\t g:i A')); ?></p>
                                </div>

                                <?php if($meeting->end_time): ?>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">End Time</label>
                                        <p class="mt-1 text-sm text-gray-900"><?php echo e($meeting->end_time->format('F j, Y \a\t g:i A')); ?></p>
                                    </div>
                                <?php endif; ?>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Status</label>
                                    <span class="mt-1 px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                        <?php if($meeting->status === 'scheduled'): ?> bg-yellow-100 text-yellow-800
                                        <?php elseif($meeting->status === 'active'): ?> bg-green-100 text-green-800
                                        <?php elseif($meeting->status === 'completed'): ?> bg-blue-100 text-blue-800
                                        <?php else: ?> bg-red-100 text-red-800
                                        <?php endif; ?>">
                                        <?php echo e(ucfirst($meeting->status)); ?>

                                    </span>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Meeting Access</h3>

                            <?php if($meeting->meet_link): ?>
                                <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-green-800">Meeting Link Available</h3>
                                            <div class="mt-2">
                                                <a href="<?php echo e($meeting->meet_link); ?>" target="_blank"
                                                   class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                                    Join Google Meet
                                                </a>
                                            </div>
                                            <div class="mt-2">
                                                <p class="text-xs text-green-700">Meeting ID: <?php echo e($meeting->meet_id ?? 'N/A'); ?></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-yellow-800">No Meeting Link</h3>
                                            <p class="mt-1 text-sm text-yellow-700">The Google Meet link could not be generated. This usually means Google API is not configured.</p>
                                            <?php if(Auth::user()->isSuperAdmin()): ?>
                                                <div class="mt-2">
                                                    <a href="<?php echo e(route('admin.system-configs.index')); ?>"
                                                       class="bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-1 px-3 rounded text-xs">
                                                        Configure Google API
                                                    </a>
                                                </div>
                                            <?php else: ?>
                                                <p class="mt-1 text-xs text-yellow-600">Please contact your administrator to configure Google API integration.</p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Recording Controls -->
                            <?php if($meeting->is_recording_enabled): ?>
                                <div class="mt-4">
                                    <h4 class="text-sm font-medium text-gray-700 mb-2">Recording Controls</h4>
                                    <form action="<?php echo e(route('meetings.toggle-recording', $meeting)); ?>" method="POST">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit"
                                                class="<?php if($meeting->is_recording_active): ?> bg-red-500 hover:bg-red-700 <?php else: ?> bg-green-500 hover:bg-green-700 <?php endif; ?> text-white font-bold py-2 px-4 rounded">
                                            <?php if($meeting->is_recording_active): ?>
                                                Stop Recording
                                            <?php else: ?>
                                                Start Recording
                                            <?php endif; ?>
                                        </button>
                                    </form>
                                    <?php if($meeting->is_recording_active): ?>
                                        <p class="mt-1 text-xs text-red-600">Recording is currently active</p>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Meeting Settings -->
                    <?php if($meeting->settings): ?>
                        <div class="border-t border-gray-200 pt-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Meeting Settings</h3>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="flex items-center">
                                    <span class="<?php if($meeting->settings->host_approval_required): ?> text-green-600 <?php else: ?> text-gray-400 <?php endif; ?>">
                                        <?php if($meeting->settings->host_approval_required): ?> ✓ <?php else: ?> ✗ <?php endif; ?>
                                    </span>
                                    <span class="ml-2 text-sm text-gray-900">Host approval required</span>
                                </div>

                                <div class="flex items-center">
                                    <span class="<?php if($meeting->settings->screen_sharing_enabled): ?> text-green-600 <?php else: ?> text-gray-400 <?php endif; ?>">
                                        <?php if($meeting->settings->screen_sharing_enabled): ?> ✓ <?php else: ?> ✗ <?php endif; ?>
                                    </span>
                                    <span class="ml-2 text-sm text-gray-900">Screen sharing enabled</span>
                                </div>

                                <div class="flex items-center">
                                    <span class="<?php if($meeting->settings->mute_on_entry): ?> text-green-600 <?php else: ?> text-gray-400 <?php endif; ?>">
                                        <?php if($meeting->settings->mute_on_entry): ?> ✓ <?php else: ?> ✗ <?php endif; ?>
                                    </span>
                                    <span class="ml-2 text-sm text-gray-900">Mute on entry</span>
                                </div>

                                <div class="flex items-center">
                                    <span class="<?php if($meeting->settings->chat_enabled): ?> text-green-600 <?php else: ?> text-gray-400 <?php endif; ?>">
                                        <?php if($meeting->settings->chat_enabled): ?> ✓ <?php else: ?> ✗ <?php endif; ?>
                                    </span>
                                    <span class="ml-2 text-sm text-gray-900">Chat enabled</span>
                                </div>

                                <div class="flex items-center">
                                    <span class="<?php if($meeting->settings->join_before_host): ?> text-green-600 <?php else: ?> text-gray-400 <?php endif; ?>">
                                        <?php if($meeting->settings->join_before_host): ?> ✓ <?php else: ?> ✗ <?php endif; ?>
                                    </span>
                                    <span class="ml-2 text-sm text-gray-900">Join before host</span>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\geexar\Projects\Meet\resources\views/meetings/show.blade.php ENDPATH**/ ?>