# SSL Certificate Setup Guide

## Problem
When trying to create Google Meet meetings, you might encounter this error:
```
cURL error 60: SSL certificate problem: unable to get local issuer certificate
```

This is a common issue on Windows development environments where PHP/cURL cannot verify SSL certificates.

## Solutions

### Option 1: Use Downloaded CA Bundle (Recommended)
The application will automatically use the CA certificate bundle if available:

1. **Download CA Bundle** (already done):
   ```bash
   curl -k -o storage/app/cacert.pem https://curl.se/ca/cacert.pem
   ```

2. **Verify the file exists**:
   - Check that `storage/app/cacert.pem` exists
   - The application will automatically detect and use this file

### Option 2: Manual Download
If the automatic download fails:

1. **Download manually**:
   - Visit: https://curl.se/ca/cacert.pem
   - Save the file as `storage/app/cacert.pem`

### Option 3: Development Mode (Current Setting)
For development, SSL verification is disabled automatically when:
- No CA bundle is found
- Environment is not production

**Note**: This is not recommended for production use.

## How It Works

The `GoogleMeetService` automatically:

1. **Checks for CA bundle** in common locations:
   - `storage/app/cacert.pem` (Laravel storage)
   - `C:\xampp\apache\bin\curl-ca-bundle.crt` (XAMPP)
   - System default locations

2. **Uses appropriate SSL settings**:
   - **Development**: Uses CA bundle if available, otherwise disables SSL verification
   - **Production**: Always enables SSL verification

3. **Logs SSL configuration**:
   - Check `storage/logs/laravel.log` for SSL-related messages

## Testing

After setting up SSL certificates, try creating a new meeting to test Google Meet integration.

## Production Deployment

For production environments:
1. Ensure proper CA bundle is available
2. Set `APP_ENV=production` in `.env`
3. SSL verification will be automatically enabled

## Troubleshooting

If you still encounter SSL issues:

1. **Check the log file** (`storage/logs/laravel.log`) for detailed error messages
2. **Verify CA bundle** exists and is readable
3. **Try manual download** if automatic download failed
4. **Contact support** if issues persist

## Alternative: System-wide PHP Configuration

You can also configure PHP globally by editing `php.ini`:
```ini
curl.cainfo = "C:\path\to\cacert.pem"
openssl.cafile = "C:\path\to\cacert.pem"
```

Then restart your web server.
